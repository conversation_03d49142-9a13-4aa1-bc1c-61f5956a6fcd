#pragma once

#include <stdint.h>

#include "hardware/pio.h"
#include "pico_pio_loader/pico_pio_loader.h"
#include "data.h"
#include "encoder.h"
#include "decoder.h"
#include "utilities.h"
#include "pio/dshot_bidir_300.pio.h"
#include "pio/dshot_bidir_600.pio.h"

namespace DShot
{

    // todo: Encoder/Decoder should by parent classes, with type/poles/etc protected
    class ESC
    {
    public:
        ESC(uint dshot_gpio, PIO pio = pio0,
            Type type = Type::Bidir,
            Speed speed = Speed::DS300,
            unsigned int poles = 14)
            : dshot_gpio(dshot_gpio), pio(pio), speed(speed), type(type), encoder(type), decoder(poles) {}

        // Todo: move internals to PIOWrapper class: Init PIO, but do not output data yet
        bool init()
        {
            pio_sm = pio_claim_unused_sm(pio, /*required=*/false);
            if (pio_sm < 0)
            {
                return false;
            }
            const pio_program_t *dshot_program = nullptr;
            void (*init_dshot_program)(PIO, uint, uint, uint) = nullptr;

            if (speed == Speed::DS300 && type == Type::Bidir)
            {
                dshot_program = &dshot_bidir_300_program;
                init_dshot_program = &dshot_bidir_300_program_init;
            }
            else if (speed == Speed::DS600 && type == Type::Bidir)
            {
                dshot_program = &dshot_bidir_600_program;
                init_dshot_program = &dshot_bidir_600_program_init;
            }
            else
            {
                // todo: some error about unsupported combos?
                return false;
            }

            if (!pio_loader_add_or_get_offset(pio, dshot_program, &pio_offset))
            {
                pio_sm_unclaim(pio, pio_sm);
                pio_sm = -1;
                return false;
            }

            // call the
            (*init_dshot_program)(pio, pio_sm, pio_offset, dshot_gpio);
            pio_sm_set_enabled(pio, pio_sm, true);
            return true;
        }

        void enableExtendedTelemetry()
        {
            setCommand(13);
        }

        // Set commands trigger once, and must be re-triggered at >= ~200Hz to work
        void setCommand(uint16_t c) // Set the DShot command value
        {
            pio_sm_put(pio, pio_sm, encoder.encode(c));
        }
        // uint16_t setThrottleServo(uint16_t t); // Set thottle in range [1000, 2000] (servo pwm)
        // uint16_t setThrottle3D(double t); // Set the throttle in range [-1, 1]
        // uint16_t setThrottleServo3D(int16_t t); // Throttle range [1000, 2000], 1500 is 0 if ESC is 3d
        void setThrottle(double t) // Set the throttle in range [0, 1]
        {
            // https://github.com/betaflight/betaflight/issues/2879
            if (t < 0)
                t = 0;
            if (t > 1)
                t = 1;

            uint16_t c = MIN_THROTTLE_COMMAND + t * (MAX_THROTTLE_COMMAND - MIN_THROTTLE_COMMAND);
            if (c < MIN_THROTTLE_COMMAND)
                c = MIN_THROTTLE_COMMAND;
            if (c > MAX_THROTTLE_COMMAND)
                c = MAX_THROTTLE_COMMAND;
            setCommand(c);
        }

        void setStop()
        {
            setCommand(0);
        }

        int getRawTelemetry(uint64_t &raw_telemetry) // get the currently raw telemetry results, true if there are some
        {
            if (type == Type::Normal)
                return false;

            int fifo_words = pio_sm_get_rx_fifo_level(pio, pio_sm);
            if (fifo_words >= 2)
            {
                raw_telemetry = (uint64_t)pio_sm_get_blocking(pio, pio_sm) << 32;
                raw_telemetry |= (uint64_t)pio_sm_get_blocking(pio, pio_sm);
                return true;
            }
            return false;
        }
        bool getAndDecodeTelemetry(Telemetry &telemetry)
        {
            uint64_t raw_telemetry;
            if (!getRawTelemetry(raw_telemetry)) {
                return false;
            }
            return decoder.decodeTelemetry(raw_telemetry, telemetry);
        }

        Telemetry telemetry = {0}; // todo: use?

    private:
        // todo: Also move the PIO layer to its own DShot::PIO(gpio, pio, bidir, speed) class
        uint dshot_gpio;
        PIO pio;
        uint pio_offset;

        int pio_sm = -1;
        const Speed speed;
        const Type type;
        Encoder encoder;
        Decoder decoder;
    };

} // end namespace DShot
