// -------------------------------------------------- //
// This file is autogenerated by pioasm; do not edit! //
// -------------------------------------------------- //

#pragma once

#if !PICO_NO_HARDWARE
#include "hardware/pio.h"
#endif

// --------------- //
// dshot_bidir_300 //
// --------------- //

#define dshot_bidir_300_wrap_target 0
#define dshot_bidir_300_wrap 30

#define dshot_bidir_300_BIT_PERIOD 40

static const uint16_t dshot_bidir_300_program_instructions[] = {
            //     .wrap_target
    0xff81, //  0: set    pindirs, 1             [31]
    0xff01, //  1: set    pins, 1                [31]
    0x80a0, //  2: pull   block                      
    0x6050, //  3: out    y, 16                      
    0x00e6, //  4: jmp    !osre, 6                   
    0x000e, //  5: jmp    14                         
    0x6041, //  6: out    y, 1                       
    0x006b, //  7: jmp    !y, 11                     
    0xfd00, //  8: set    pins, 0                [29]
    0xe501, //  9: set    pins, 1                [5] 
    0x0004, // 10: jmp    4                          
    0xee00, // 11: set    pins, 0                [14]
    0xf401, // 12: set    pins, 1                [20]
    0x0004, // 13: jmp    4                          
    0xe04a, // 14: set    y, 10                      
    0x1f8f, // 15: jmp    y--, 15                [31]
    0xe080, // 16: set    pindirs, 0                 
    0xe05f, // 17: set    y, 31                      
    0x0075, // 18: jmp    !y, 21                     
    0x0094, // 19: jmp    y--, 20                    
    0x00d2, // 20: jmp    pin, 18                    
    0xe05e, // 21: set    y, 30                      
    0x4901, // 22: in     pins, 1                [9] 
    0x0096, // 23: jmp    y--, 22                    
    0x4801, // 24: in     pins, 1                [8] 
    0x8020, // 25: push   block                      
    0xe05f, // 26: set    y, 31                      
    0x4a01, // 27: in     pins, 1                [10]
    0x009b, // 28: jmp    y--, 27                    
    0x8020, // 29: push   block                      
    0x0000, // 30: jmp    0                          
            //     .wrap
};

#if !PICO_NO_HARDWARE
static const struct pio_program dshot_bidir_300_program = {
    .instructions = dshot_bidir_300_program_instructions,
    .length = 31,
    .origin = -1,
};

static inline pio_sm_config dshot_bidir_300_program_get_default_config(uint offset) {
    pio_sm_config c = pio_get_default_sm_config();
    sm_config_set_wrap(&c, offset + dshot_bidir_300_wrap_target, offset + dshot_bidir_300_wrap);
    return c;
}

static inline void dshot_bidir_300_program_init(PIO pio, uint sm, uint offset, uint pin) {
    pio_sm_config c = dshot_bidir_300_program_get_default_config(offset);
    sm_config_set_set_pins(&c, pin, 1);
    sm_config_set_in_pins(&c, pin);
    sm_config_set_jmp_pin (&c, pin);
    pio_gpio_init(pio, pin);
    pio_sm_set_consecutive_pindirs(pio, sm, pin, 1, true);
    gpio_set_pulls(pin, true, false);  // up, down
    sm_config_set_out_shift(&c, false, false, 32);   // auto-pull enabled
    sm_config_set_in_shift(&c, false, false, 32);
    double clocks_per_us = clock_get_hz(clk_sys) / 1000000;
    // 3.333us per bit for dshot300
    sm_config_set_clkdiv(&c, 3.333 / dshot_bidir_300_BIT_PERIOD * clocks_per_us);
    pio_sm_init(pio, sm, offset, &c);
}

#endif

