#include <Arduino.h>
#include <lib/dshot/esc.h>
#include <lib/HX711.h>
#include "hardware/timer.h"

const uint8_t DSHOT_PIN = 27;
const uint8_t HX711_DATA_PIN = 28;  // Can use any pins!
const uint8_t HX711_CLOCK_PIN = 29; // Can use any pins!

// DSHOT communication frequency (Hz) - based on current 3ms timing
const uint32_t DSHOT_FREQUENCY_HZ = 333;
const uint32_t DSHOT_PERIOD_US = 1000000 / DSHOT_FREQUENCY_HZ;

DShot::ESC dshot(DSHOT_PIN, pio0, DShot::Type::Bidir, DShot::Speed::DS300, 14);
HX711 hx711(HX711_DATA_PIN, HX711_CLOCK_PIN, CHAN_A_GAIN_128, 700);

// Global variables for DSHOT state management
volatile uint32_t dshot_tick_counter = 0;
volatile bool dshot_initialized = false;
volatile uint32_t interrupt_debug_counter = 0;

// Telemetry data - moved to global scope for interrupt access
DShot::Telemetry telemetry = {0};

// Timer interrupt callback for DSHOT communication
bool dshot_timer_callback(struct repeating_timer *t) {
    interrupt_debug_counter++; // Always increment to verify interrupt is working

    if (!dshot_initialized) {
        return true; // Continue timer but don't send commands yet
    }

    dshot_tick_counter++;

    // State machine based on tick counter (333Hz = ~1000 ticks for 3 seconds)
    if (dshot_tick_counter < 1000) {
        // First ~3 seconds: Send stop commands
        dshot.setStop();
    }
    else if (dshot_tick_counter < 1333) {
        // Next ~1 second: Enable extended telemetry
        dshot.enableExtendedTelemetry();
    }
    else {
        // After ~4 seconds: Send 10% throttle
        dshot.setThrottle(0.10);
    }

    return true; // Continue repeating
}

void setup()
{
    Serial.begin(115200);

    // wait for serial port to connect. Needed for native USB port only
    // while (!Serial) {
    //     delay(10);
    // }

    dshot.init();
    hx711.init();

    // Set up repeating timer for DSHOT communication
    static struct repeating_timer timer;
    add_repeating_timer_us(-DSHOT_PERIOD_US, dshot_timer_callback, NULL, &timer);

    // Mark DSHOT as initialized and reset counter
    dshot_tick_counter = 0;
    dshot_initialized = true;
}

void loop()
{
    // Read telemetry data in main loop (not in interrupt)
    dshot.getAndDecodeTelemetry(telemetry);
    if (telemetry.reads % 1000 == 0) {
        telemetry.errors = 0;
        telemetry.reads = 0;
    }

    // Print weight and telemetry data only if we have recent data
    if (hx711.getLastReadTime() > 0)
    {
        Serial.print("Weight: ");
        Serial.print(hx711.getLatestWeight());
        Serial.print("g, ");

        Serial.print("RPM: ");
        Serial.print(telemetry.rpm);
        Serial.print(", Temp: ");
        Serial.print(telemetry.temperature_C);
        Serial.print("C, Voltage: ");
        Serial.print(telemetry.volts_cV / 100);
        Serial.print(".");
        Serial.print(telemetry.volts_cV % 100);
        Serial.print("V, Current: ");
        Serial.print(telemetry.amps_A);
        Serial.print("A, Errors: ");
        Serial.print(telemetry.errors);
        Serial.print("/");
        Serial.print(telemetry.reads);

        // Debug info to verify timer is working
        Serial.print(", Timer: ");
        Serial.print(interrupt_debug_counter);
        Serial.print(", Ticks: ");
        Serial.print(dshot_tick_counter);

        Serial.println("");
    }

    // Small delay to allow PIO processing and reasonable print rate
    delay(3);  // Similar to original timing
}
