#include <Arduino.h>
#include <lib/dshot/esc.h>
#include <lib/HX711.h>

const uint8_t DSHOT_PIN = 27;
const uint8_t HX711_DATA_PIN = 28;  // Can use any pins!
const uint8_t HX711_CLOCK_PIN = 29; // Can use any pins!

DShot::ESC dshot(DSHOT_PIN, pio0, DShot::Type::Bidir, DShot::Speed::DS300, 14);
HX711 hx711(HX711_DATA_PIN, HX711_CLOCK_PIN, CHAN_A_GAIN_128, 700);

void setup()
{
    Serial.begin(115200);

    // wait for serial port to connect. Needed for native USB port only
    // while (!Serial) {
    //     delay(10);
    // }

    dshot.init();
    hx711.init();
}

// the loop routine runs over and over again forever:
// todo: remove telemetry from here, include in dshot class
DShot::Telemetry telemetry = {0};
void loop()
{
    delay(2);
    // Serial.print("dshot: ");
    // print_bin(dshot.setCommand(1046)); // 1046 is the example command
    if (millis() < 3000) {
        dshot.setStop();
    }
    else if (millis() < 4000)
    {
        dshot.enableExtendedTelemetry(); // extended telemetry enable
    }
    else
    {
        dshot.setThrottle(0.10);
    }
    delay(1);                    // wait for dshot PIO to be done

    dshot.getAndDecodeTelemetry(telemetry);
    if (telemetry.reads % 1000 == 0)
    {
        telemetry.errors = 0;
        telemetry.reads = 0;
    }

    // Print weight data only if we have recent data and enough time has passed
    if (millis() > 5000 && hx711.getLastReadTime() > 0)
    {
        Serial.print("Weight: ");
        Serial.print(hx711.getLatestWeight());
        Serial.println("g");

        Serial.print(telemetry.rpm);
        Serial.print(", ");
        Serial.print(telemetry.temperature_C);
        Serial.print(", ");
        Serial.print(telemetry.volts_cV / 100);
        Serial.print(".");
        Serial.print(telemetry.volts_cV % 100);
        Serial.print(", ");
        Serial.print(telemetry.amps_A);
        Serial.print(", ");
        Serial.print(telemetry.errors);
        Serial.print("/");
        Serial.print(telemetry.reads);
        Serial.println("");
    }
}
