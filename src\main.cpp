#include <Arduino.h>
#include <lib/dshot/esc.h>
#include <lib/HX711.h>
#include "hardware/timer.h"

const uint8_t DSHOT_PIN = 27;
const uint8_t HX711_DATA_PIN = 28;  // Can use any pins!
const uint8_t HX711_CLOCK_PIN = 29; // Can use any pins!

// DSHOT communication frequency (Hz) - based on current 3ms timing
const uint32_t DSHOT_FREQUENCY_HZ = 333;
const uint32_t DSHOT_PERIOD_US = 1000000 / DSHOT_FREQUENCY_HZ;

DShot::ESC dshot(DSHOT_PIN, pio0, DShot::Type::Bidir, DShot::Speed::DS300, 14);
HX711 hx711(HX711_DATA_PIN, HX711_CLOCK_PIN, CHAN_A_GAIN_128, 700);

// Global variables for DSHOT state management
volatile uint32_t dshot_start_time_ms = 0;
volatile bool dshot_initialized = false;

// Telemetry data - moved to global scope for interrupt access
DShot::Telemetry telemetry = {0};

// Timer interrupt callback for DSHOT communication
bool dshot_timer_callback(struct repeating_timer *t) {
    if (!dshot_initialized) {
        return true; // Continue timer but don't send commands yet
    }

    uint32_t current_time_ms = millis();
    uint32_t elapsed_ms = current_time_ms - dshot_start_time_ms;

    // State machine for DSHOT commands based on elapsed time
    if (elapsed_ms < 3000) {
        // First 3 seconds: Send stop commands
        dshot.setStop();

    }
    else if (elapsed_ms < 4000) {
        // Next 1 second: Enable extended telemetry
        dshot.enableExtendedTelemetry();
    }
    else {
        // After 4 seconds: Send 10% throttle
        dshot.setThrottle(0.10);
    }

    // Small delay to allow PIO to process the command
    // Note: This is a very short delay in interrupt context
    delayMicroseconds(100);

    // Read telemetry data
    dshot.getAndDecodeTelemetry(telemetry);
    if (telemetry.reads % 1000 == 0) {
        telemetry.errors = 0;
        telemetry.reads = 0;
    }

    return true; // Continue repeating
}

void setup()
{
    Serial.begin(115200);

    // wait for serial port to connect. Needed for native USB port only
    // while (!Serial) {
    //     delay(10);
    // }

    dshot.init();
    hx711.init();

    // Record start time for DSHOT state machine
    dshot_start_time_ms = millis();

    // Set up repeating timer for DSHOT communication
    static struct repeating_timer timer;
    add_repeating_timer_us(-DSHOT_PERIOD_US, dshot_timer_callback, NULL, &timer);

    // Mark DSHOT as initialized
    dshot_initialized = true;
}

// Main loop now only handles printing - DSHOT communication is in interrupt
void loop()
{
    // Print weight and telemetry data only if we have recent data and enough time has passed
    if (hx711.getLastReadTime() > 0)
    {
        // Create local copy of telemetry data to avoid race conditions
        DShot::Telemetry local_telemetry;
        noInterrupts();
        local_telemetry = telemetry;
        interrupts();

        Serial.print("Weight: ");
        Serial.print(hx711.getLatestWeight());
        Serial.print("g, ");

        Serial.print("RPM: ");
        Serial.print(local_telemetry.rpm);
        Serial.print(", Temp: ");
        Serial.print(local_telemetry.temperature_C);
        Serial.print("C, Voltage: ");
        Serial.print(local_telemetry.volts_cV / 100);
        Serial.print(".");
        Serial.print(local_telemetry.volts_cV % 100);
        Serial.print("V, Current: ");
        Serial.print(local_telemetry.amps_A);
        Serial.print("A, Errors: ");
        Serial.print(local_telemetry.errors);
        Serial.print("/");
        Serial.print(local_telemetry.reads);
        Serial.println("");
    }

    // Main loop can now run at a more relaxed pace since DSHOT is handled by interrupt
    delay(100);  // 10Hz print rate
}
